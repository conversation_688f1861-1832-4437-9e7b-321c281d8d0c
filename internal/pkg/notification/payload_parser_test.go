package notification

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseFCMPayload(t *testing.T) {
	tests := []struct {
		name        string
		payload     map[string]interface{}
		expected    *PayloadData
		expectError bool
	}{
		{
			name: "valid payload",
			payload: map[string]interface{}{
				"event_type": "add_agent",
				"data":       `{"actor_name":"Kendall Jast DVM","extra":{"room":{"name":"<PERSON><PERSON>zmann","channel":"wa"}}}`,
			},
			expected: &PayloadData{
				ActorName:   "Kendall Jast DVM",
				RoomName:    "<PERSON><PERSON>",
				ChannelName: "wa",
				EventType:   "add_agent",
			},
			expectError: false,
		},
		{
			name: "missing event_type",
			payload: map[string]interface{}{
				"data": `{"actor_name":"Kendall Jast DVM","extra":{"room":{"name":"<PERSON><PERSON>","channel":"wa"}}}`,
			},
			expected:    nil,
			expectError: true,
		},
		{
			name: "missing data field",
			payload: map[string]interface{}{
				"event_type": "add_agent",
			},
			expected:    nil,
			expectError: true,
		},
		{
			name: "invalid JSO<PERSON> in data field",
			payload: map[string]interface{}{
				"event_type": "add_agent",
				"data":       `invalid json`,
			},
			expected:    nil,
			expectError: true,
		},
		{
			name: "missing actor_name",
			payload: map[string]interface{}{
				"event_type": "add_agent",
				"data":       `{"extra":{"room":{"name":"Rena Kertzmann","channel":"wa"}}}`,
			},
			expected:    nil,
			expectError: true,
		},
		{
			name: "missing room info",
			payload: map[string]interface{}{
				"event_type": "add_agent",
				"data":       `{"actor_name":"Kendall Jast DVM"}`,
			},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseFCMPayload(tt.payload)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)
				assert.Equal(t, tt.expected.ActorName, result.ActorName)
				assert.Equal(t, tt.expected.RoomName, result.RoomName)
				assert.Equal(t, tt.expected.ChannelName, result.ChannelName)
				assert.Equal(t, tt.expected.EventType, result.EventType)
			}
		})
	}
}

func TestPayloadData_BuildDescription(t *testing.T) {
	payloadData := &PayloadData{
		ActorName:   "Kendall Jast DVM",
		RoomName:    "Rena Kertzmann",
		ChannelName: "wa",
		EventType:   "add_agent",
	}

	expected := "Kendall Jast DVM assigned you Rena Kertzmann on wa"
	result := payloadData.BuildDescription()

	assert.Equal(t, expected, result)
}

func TestExtractEventType(t *testing.T) {
	tests := []struct {
		name        string
		data        map[string]interface{}
		expected    string
		expectError bool
	}{
		{
			name:        "valid event_type",
			data:        map[string]interface{}{"event_type": "add_agent"},
			expected:    "add_agent",
			expectError: false,
		},
		{
			name:        "missing event_type",
			data:        map[string]interface{}{},
			expected:    "",
			expectError: true,
		},
		{
			name:        "non-string event_type",
			data:        map[string]interface{}{"event_type": 123},
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := extractEventType(tt.data)

			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestExtractDataString(t *testing.T) {
	tests := []struct {
		name        string
		data        map[string]interface{}
		expected    string
		expectError bool
	}{
		{
			name:        "valid data string",
			data:        map[string]interface{}{"data": `{"test": "value"}`},
			expected:    `{"test": "value"}`,
			expectError: false,
		},
		{
			name:        "missing data field",
			data:        map[string]interface{}{},
			expected:    "",
			expectError: true,
		},
		{
			name:        "non-string data field",
			data:        map[string]interface{}{"data": 123},
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := extractDataString(tt.data)

			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestExtractActorName(t *testing.T) {
	tests := []struct {
		name        string
		data        map[string]interface{}
		expected    string
		expectError bool
	}{
		{
			name:        "valid actor_name",
			data:        map[string]interface{}{"actor_name": "John Doe"},
			expected:    "John Doe",
			expectError: false,
		},
		{
			name:        "missing actor_name",
			data:        map[string]interface{}{},
			expected:    "",
			expectError: true,
		},
		{
			name:        "non-string actor_name",
			data:        map[string]interface{}{"actor_name": 123},
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := extractActorName(tt.data)

			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestExtractRoomInfo(t *testing.T) {
	tests := []struct {
		name            string
		data            map[string]interface{}
		expectedRoom    string
		expectedChannel string
		expectError     bool
	}{
		{
			name: "valid room info",
			data: map[string]interface{}{
				"extra": map[string]interface{}{
					"room": map[string]interface{}{
						"name":    "Test Room",
						"channel": "wa",
					},
				},
			},
			expectedRoom:    "Test Room",
			expectedChannel: "wa",
			expectError:     false,
		},
		{
			name:            "missing extra",
			data:            map[string]interface{}{},
			expectedRoom:    "",
			expectedChannel: "",
			expectError:     true,
		},
		{
			name: "missing room",
			data: map[string]interface{}{
				"extra": map[string]interface{}{},
			},
			expectedRoom:    "",
			expectedChannel: "",
			expectError:     true,
		},
		{
			name: "missing room name",
			data: map[string]interface{}{
				"extra": map[string]interface{}{
					"room": map[string]interface{}{
						"channel": "wa",
					},
				},
			},
			expectedRoom:    "",
			expectedChannel: "",
			expectError:     true,
		},
		{
			name: "missing channel",
			data: map[string]interface{}{
				"extra": map[string]interface{}{
					"room": map[string]interface{}{
						"name": "Test Room",
					},
				},
			},
			expectedRoom:    "",
			expectedChannel: "",
			expectError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			roomName, channelName, err := extractRoomInfo(tt.data)

			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, roomName)
				assert.Empty(t, channelName)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedRoom, roomName)
				assert.Equal(t, tt.expectedChannel, channelName)
			}
		})
	}
}
