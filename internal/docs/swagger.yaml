definitions:
  domains.ErrorDetail:
    description: Error detail structure
    properties:
      error:
        description: Error message
        example: Invalid input
        type: string
    type: object
  domains.PaginationMeta:
    description: Pagination metadata structure
    properties:
      limit:
        description: Number of items per page
        example: 10
        type: integer
      page:
        description: Current page number
        example: 1
        type: integer
      total:
        description: Total number of items
        example: 100
        type: integer
      total_page:
        description: Total number of pages
        example: 10
        type: integer
    type: object
  domains.Response:
    description: Standard API response structure for both success and error cases
    properties:
      data:
        description: Data contains the response payload for successful requests
        type: object
      errors:
        description: Errors contains error details when the request fails
        items:
          type: object
        type: array
      meta:
        description: Meta contains metadata about the response (pagination info, messages,
          etc.)
        type: object
    type: object
  handler.CreateNotificationRequest:
    properties:
      click_action:
        allOf:
        - $ref: '#/definitions/model.ClickAction'
        example: OPEN_URL
      click_action_url:
        example: https://example.com/message/123
        type: string
      description:
        example: You have a new message
        type: string
      is_reminder:
        example: false
        type: boolean
      notif_category_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      notif_type_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      user_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
    required:
    - click_action
    - description
    - user_id
    type: object
  handler.RegisterTokenRequest:
    properties:
      sso_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      token:
        example: fcm_token_string
        type: string
      user_source:
        allOf:
        - $ref: '#/definitions/model.UserSource'
        example: mobile_app
      user_type:
        allOf:
        - $ref: '#/definitions/model.UserType'
        example: customer
    required:
    - sso_id
    - token
    - user_source
    - user_type
    type: object
  handler.SendPushNotificationRequest:
    type: object
  model.ClickAction:
    enum:
    - OPEN_URL
    - OPEN_APP
    type: string
    x-enum-varnames:
    - ClickActionOpenURL
    - ClickActionOpenApp
  model.FCMToken:
    properties:
      created_at:
        type: string
      id:
        type: string
      sso_id:
        type: string
      token:
        type: string
      updated_at:
        type: string
      user_source:
        $ref: '#/definitions/model.UserSource'
      user_type:
        $ref: '#/definitions/model.UserType'
    type: object
  model.Notification:
    properties:
      click_action:
        $ref: '#/definitions/model.ClickAction'
      click_action_url:
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        type: string
      is_reminder:
        type: boolean
      notif_category_id:
        type: string
      notif_type_id:
        type: string
      read_at:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  model.UserSource:
    enum:
    - crm
    - chat
    - notification_service
    type: string
    x-enum-varnames:
    - UserSourceCRM
    - UserSourceChat
    - UserSourceNotificationSvc
  model.UserType:
    enum:
    - admin
    - supervisor
    - agent
    type: string
    x-enum-varnames:
    - UserTypeAdmin
    - UserTypeSupervisor
    - UserTypeAgent
info:
  contact: {}
paths:
  /api/v1/fcm-tokens:
    post:
      consumes:
      - application/json
      description: Register a new Firebase Cloud Messaging token for a user
      parameters:
      - description: FCM token registration details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.RegisterTokenRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Token registered successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/model.FCMToken'
              type: object
        "400":
          description: Invalid request body or missing required fields
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Register FCM token
      tags:
      - fcm-tokens
  /api/v1/fcm-tokens/{id}:
    delete:
      description: Delete a specific FCM token by its ID
      parameters:
      - description: Token ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Token deleted successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                message:
                  type: string
              type: object
        "400":
          description: Invalid token ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "404":
          description: Token not found
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Delete FCM token
      tags:
      - fcm-tokens
  /api/v1/fcm-tokens/sso/{sso_id}:
    get:
      description: Retrieve all FCM tokens associated with a specific SSO ID
      parameters:
      - description: SSO ID (UUID)
        format: uuid
        in: path
        name: sso_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of FCM tokens
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/model.FCMToken'
                  type: array
              type: object
        "400":
          description: Invalid SSO ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Get FCM tokens by SSO ID
      tags:
      - fcm-tokens
  /api/v1/health:
    get:
      description: Check if the service is healthy and running
      produces:
      - application/json
      responses:
        "200":
          description: Service is healthy
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                message:
                  type: string
              type: object
      summary: Health check
      tags:
      - health
  /api/v1/notifications:
    get:
      description: Retrieve a paginated list of notifications
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        minimum: 1
        name: page
        type: integer
      - description: 'Number of items per page (default: 10, max: 100)'
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of notifications with pagination metadata
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/model.Notification'
                  type: array
                meta:
                  $ref: '#/definitions/domains.PaginationMeta'
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Get all notifications
      tags:
      - notifications
    post:
      consumes:
      - application/json
      description: Create a new notification for a specific user
      parameters:
      - description: Notification details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.CreateNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Notification created successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/model.Notification'
              type: object
        "400":
          description: Invalid request body
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Create a new notification
      tags:
      - notifications
  /api/v1/notifications/{id}:
    get:
      description: Retrieve a specific notification by its ID
      parameters:
      - description: Notification ID (UUID)
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Notification details
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                data:
                  $ref: '#/definitions/model.Notification'
              type: object
        "400":
          description: Invalid notification ID
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "404":
          description: Notification not found
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Get notification by ID
      tags:
      - notifications
  /api/v1/push-notifications:
    post:
      consumes:
      - application/json
      description: Send push notifications to multiple users by their SSO IDs
      parameters:
      - description: Push notification details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.SendPushNotificationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Push notification sent successfully
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                message:
                  type: string
              type: object
        "400":
          description: Invalid request body or missing required fields
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
        "500":
          description: Internal server error
          schema:
            allOf:
            - $ref: '#/definitions/domains.Response'
            - properties:
                errors:
                  items:
                    $ref: '#/definitions/domains.ErrorDetail'
                  type: array
              type: object
      summary: Send push notification
      tags:
      - push-notifications
swagger: "2.0"
