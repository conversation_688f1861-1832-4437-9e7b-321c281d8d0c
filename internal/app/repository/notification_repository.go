package repository

import (
	"context"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationRepository defines the interface for notification repository operations
type NotificationRepository interface {
	Create(ctx context.Context, notification *model.Notification) error
	CreateBatch(ctx context.Context, notifications []*model.Notification) error
	FindByID(ctx context.Context, id uuid.UUID) (*model.Notification, error)
	FindAll(ctx context.Context, page, limit int) ([]model.Notification, int64, error)
}

type notificationRepository struct {
	db *gorm.DB
}

// NewNotificationRepository creates a new notification repository
func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{
		db: db,
	}
}

// Create creates a new notification
func (r *notificationRepository) Create(ctx context.Context, notification *model.Notification) error {
	return r.db.WithContext(ctx).Create(notification).Error
}

// C<PERSON><PERSON>atch creates multiple notifications in a single transaction
func (r *notificationRepository) CreateBatch(ctx context.Context, notifications []*model.Notification) error {
	if len(notifications) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(notifications).Error
}

// FindByID finds a notification by ID
func (r *notificationRepository) FindByID(ctx context.Context, id uuid.UUID) (*model.Notification, error) {
	var notification model.Notification
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&notification).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

// FindAll finds all notifications with pagination
func (r *notificationRepository) FindAll(ctx context.Context, page, limit int) ([]model.Notification, int64, error) {
	var notifications []model.Notification
	var total int64

	// Calculate offset
	offset := (page - 1) * limit

	// Count total records
	if err := r.db.WithContext(ctx).Model(&model.Notification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get records with pagination
	if err := r.db.WithContext(ctx).Offset(offset).Limit(limit).Order("created_at DESC").Find(&notifications).Error; err != nil {
		return nil, 0, err
	}

	return notifications, total, nil
}
