package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"bitbucket.org/terbang-ventures/notification-service/internal/app/model"
	"bitbucket.org/terbang-ventures/notification-service/internal/app/service"
	httpLib "bitbucket.org/terbang-ventures/notification-service/pkg/http"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
)

// NotificationHandler defines the interface for notification handler operations
type NotificationHandler interface {
	CreateNotification(w http.ResponseWriter, r *http.Request)
	GetNotificationByID(w http.ResponseWriter, r *http.Request)
	GetNotifications(w http.ResponseWriter, r *http.Request)
}

type notificationHandler struct {
	notificationService service.NotificationService
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationService service.NotificationService) NotificationHandler {
	return &notificationHandler{
		notificationService: notificationService,
	}
}

// CreateNotificationRequest represents the request body for creating a notification
type CreateNotificationRequest struct {
	UserID          uuid.UUID         `json:"user_id" validate:"required" example:"123e4567-e89b-12d3-a456-************" description:"User's unique identifier"`
	Description     string            `json:"description" validate:"required" example:"You have a new message" description:"Content of the notification"`
	ClickAction     model.ClickAction `json:"click_action" validate:"required" example:"OPEN_URL" description:"Action to be performed when notification is clicked"`
	ClickActionURL  string            `json:"click_action_url" example:"https://example.com/message/123" description:"URL to open when notification is clicked"`
	IsReminder      bool              `json:"is_reminder" example:"false" description:"Whether this notification is a reminder"`
	NotifTypeID     uuid.UUID         `json:"notif_type_id" example:"123e4567-e89b-12d3-a456-************" description:"Type of notification"`
	NotifCategoryID uuid.UUID         `json:"notif_category_id" example:"123e4567-e89b-12d3-a456-************" description:"Category of notification"`
}

// @Summary Create a new notification
// @Description Create a new notification for a specific user
// @Tags notifications
// @Accept json
// @Produce json
// @Param body body CreateNotificationRequest true "Notification details"
// @Success 201 {object} domains.Response{data=model.Notification} "Notification created successfully"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid request body"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications [post]
func (h *notificationHandler) CreateNotification(w http.ResponseWriter, r *http.Request) {
	var req CreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid request body"}})
		return
	}

	notification := &model.Notification{
		UserID:          req.UserID,
		Description:     req.Description,
		ClickAction:     req.ClickAction,
		ClickActionURL:  req.ClickActionURL,
		IsReminder:      req.IsReminder,
		NotifTypeID:     req.NotifTypeID,
		NotifCategoryID: req.NotifCategoryID,
	}

	if err := h.notificationService.CreateNotification(r.Context(), notification); err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusCreated, notification, nil)
}

// @Summary Get notification by ID
// @Description Retrieve a specific notification by its ID
// @Tags notifications
// @Produce json
// @Param id path string true "Notification ID (UUID)" format(uuid)
// @Success 200 {object} domains.Response{data=model.Notification} "Notification details"
// @Failure 400 {object} domains.Response{errors=[]domains.ErrorDetail} "Invalid notification ID"
// @Failure 404 {object} domains.Response{errors=[]domains.ErrorDetail} "Notification not found"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications/{id} [get]
func (h *notificationHandler) GetNotificationByID(w http.ResponseWriter, r *http.Request) {
	idParam := chi.URLParam(r, "id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		httpLib.BadRequest(w, []map[string]string{{"error": "Invalid notification ID"}})
		return
	}

	notification, err := h.notificationService.GetNotificationByID(r.Context(), id)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notification, nil)
}

// @Summary Get all notifications
// @Description Retrieve a paginated list of notifications
// @Tags notifications
// @Produce json
// @Param page query integer false "Page number (default: 1)" minimum(1)
// @Param limit query integer false "Number of items per page (default: 10, max: 100)" minimum(1) maximum(100)
// @Success 200 {object} domains.Response{data=[]model.Notification,meta=domains.PaginationMeta} "List of notifications with pagination metadata"
// @Failure 500 {object} domains.Response{errors=[]domains.ErrorDetail} "Internal server error"
// @Router /api/v1/notifications [get]
func (h *notificationHandler) GetNotifications(w http.ResponseWriter, r *http.Request) {
	// Parse pagination parameters
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	notifications, total, err := h.notificationService.GetNotifications(r.Context(), page, limit)
	if err != nil {
		httpLib.Error(w, err)
		return
	}

	// Create pagination metadata
	meta := map[string]interface{}{
		"page":       page,
		"limit":      limit,
		"total":      total,
		"total_page": (total + int64(limit) - 1) / int64(limit),
	}

	httpLib.WriteSuccessResponse(w, http.StatusOK, notifications, meta)
}
