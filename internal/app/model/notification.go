package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ClickAction represents the possible actions when a notification is clicked
type ClickAction string

const (
	// Define possible click actions
	ClickActionOpenURL ClickAction = "OPEN_URL"
	ClickActionOpenApp ClickAction = "OPEN_APP"
)

// Notification represents a notification in the system
type Notification struct {
	ID              uuid.UUID   `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID          uuid.UUID   `json:"user_id" gorm:"type:uuid;not null"`
	Description     string      `json:"description" gorm:"type:text;not null"`
	ClickAction     ClickAction `json:"click_action" gorm:"type:varchar(50);not null"`
	ClickActionURL  string      `json:"click_action_url" gorm:"type:text"`
	EventType       string      `json:"event_type" gorm:"type:varchar(100)"`
	ReadAt          *time.Time  `json:"read_at" gorm:"type:timestamp"`
	IsReminder      bool        `json:"is_reminder" gorm:"type:boolean;default:false"`
	NotifTypeID     uuid.UUID   `json:"notif_type_id" gorm:"type:uuid"`
	NotifCategoryID uuid.UUID   `json:"notif_category_id" gorm:"type:uuid"`
	CreatedAt       time.Time   `json:"created_at" gorm:"type:timestamp;not null;default:now()"`
	UpdatedAt       time.Time   `json:"updated_at" gorm:"type:timestamp;not null;default:now()"`
}

// TableName specifies the table name for the Notification model
func (Notification) TableName() string {
	return "notifications"
}

// BeforeCreate is a hook that runs before creating a new notification
func (n *Notification) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	n.CreatedAt = time.Now()
	n.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate is a hook that runs before updating a notification
func (n *Notification) BeforeUpdate(tx *gorm.DB) error {
	n.UpdatedAt = time.Now()
	return nil
}
