package model

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestNotification_TableName(t *testing.T) {
	notification := Notification{}
	assert.Equal(t, "notifications", notification.TableName())
}

func TestNotification_BeforeCreate(t *testing.T) {
	// Create a notification with nil ID
	notification := Notification{
		Description: "Test notification",
		UserID:      uuid.New(),
		ClickAction: ClickActionOpenURL,
		EventType:   "add_agent",
	}

	// Call BeforeCreate
	err := notification.BeforeCreate(&gorm.DB{})
	
	// Assert no error
	assert.NoError(t, err)
	
	// Assert ID is set
	assert.NotEqual(t, uuid.Nil, notification.ID)
	
	// Assert timestamps are set
	assert.False(t, notification.CreatedAt.IsZero())
	assert.False(t, notification.UpdatedAt.IsZero())
}

func TestNotification_BeforeUpdate(t *testing.T) {
	// Create a notification with a specific UpdatedAt time
	oldTime := time.Now().Add(-24 * time.Hour)
	notification := Notification{
		UpdatedAt: oldTime,
	}

	// Call BeforeUpdate
	err := notification.BeforeUpdate(&gorm.DB{})
	
	// Assert no error
	assert.NoError(t, err)
	
	// Assert UpdatedAt is changed
	assert.True(t, notification.UpdatedAt.After(oldTime))
}

func TestClickAction_Constants(t *testing.T) {
	// Test that the ClickAction constants are defined correctly
	assert.Equal(t, ClickAction("OPEN_URL"), ClickActionOpenURL)
	assert.Equal(t, ClickAction("OPEN_APP"), ClickActionOpenApp)
}
