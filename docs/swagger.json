{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/v1/fcm-tokens": {"post": {"description": "Register a new Firebase Cloud Messaging token for a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["fcm-tokens"], "summary": "Register FCM token", "parameters": [{"description": "FCM token registration details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handler.RegisterTokenRequest"}}], "responses": {"201": {"description": "Token registered successfully", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/model.FCMToken"}}}]}}, "400": {"description": "Invalid request body or missing required fields", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "500": {"description": "Internal server error", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}}}}, "/api/v1/fcm-tokens/sso/{sso_id}": {"get": {"description": "Retrieve all FCM tokens associated with a specific SSO ID", "produces": ["application/json"], "tags": ["fcm-tokens"], "summary": "Get FCM tokens by SSO ID", "parameters": [{"type": "string", "format": "uuid", "description": "SSO ID (UUID)", "name": "sso_id", "in": "path", "required": true}], "responses": {"200": {"description": "List of FCM tokens", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/model.FCMToken"}}}}]}}, "400": {"description": "Invalid SSO ID", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "500": {"description": "Internal server error", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}}}}, "/api/v1/fcm-tokens/{id}": {"delete": {"description": "Delete a specific FCM token by its ID", "produces": ["application/json"], "tags": ["fcm-tokens"], "summary": "Delete FCM token", "parameters": [{"type": "string", "format": "uuid", "description": "Token ID (UUID)", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Token deleted successfully", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}, "400": {"description": "Invalid token ID", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "404": {"description": "Token not found", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "500": {"description": "Internal server error", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}}}}, "/api/v1/health": {"get": {"description": "Check if the service is healthy and running", "produces": ["application/json"], "tags": ["health"], "summary": "Health check", "responses": {"200": {"description": "Service is healthy", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}}}, "/api/v1/notifications": {"get": {"description": "Retrieve a paginated list of notifications", "produces": ["application/json"], "tags": ["notifications"], "summary": "Get all notifications", "parameters": [{"minimum": 1, "type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "description": "Number of items per page (default: 10, max: 100)", "name": "limit", "in": "query"}], "responses": {"200": {"description": "List of notifications with pagination metadata", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/model.Notification"}}, "meta": {"$ref": "#/definitions/domains.PaginationMeta"}}}]}}, "500": {"description": "Internal server error", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}}}, "post": {"description": "Create a new notification for a specific user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["notifications"], "summary": "Create a new notification", "parameters": [{"description": "Notification details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handler.CreateNotificationRequest"}}], "responses": {"201": {"description": "Notification created successfully", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/model.Notification"}}}]}}, "400": {"description": "Invalid request body", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "500": {"description": "Internal server error", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}}}}, "/api/v1/notifications/{id}": {"get": {"description": "Retrieve a specific notification by its ID", "produces": ["application/json"], "tags": ["notifications"], "summary": "Get notification by ID", "parameters": [{"type": "string", "format": "uuid", "description": "Notification ID (UUID)", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Notification details", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/model.Notification"}}}]}}, "400": {"description": "Invalid notification ID", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "404": {"description": "Notification not found", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "500": {"description": "Internal server error", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}}}}, "/api/v1/push-notifications": {"post": {"description": "Send push notifications to multiple users by their SSO IDs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["push-notifications"], "summary": "Send push notification", "parameters": [{"description": "Push notification details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handler.SendPushNotificationRequest"}}], "responses": {"200": {"description": "Push notification sent successfully", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}, "400": {"description": "Invalid request body or missing required fields", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}, "500": {"description": "Internal server error", "schema": {"allOf": [{"$ref": "#/definitions/domains.Response"}, {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/domains.ErrorDetail"}}}}]}}}}}}, "definitions": {"domains.ErrorDetail": {"description": "Error detail structure", "type": "object", "properties": {"error": {"description": "Error message", "type": "string", "example": "Invalid input"}}}, "domains.PaginationMeta": {"description": "Pagination metadata structure", "type": "object", "properties": {"limit": {"description": "Number of items per page", "type": "integer", "example": 10}, "page": {"description": "Current page number", "type": "integer", "example": 1}, "total": {"description": "Total number of items", "type": "integer", "example": 100}, "total_page": {"description": "Total number of pages", "type": "integer", "example": 10}}}, "domains.Response": {"description": "Standard API response structure for both success and error cases", "type": "object", "properties": {"data": {"description": "Data contains the response payload for successful requests", "type": "object"}, "errors": {"description": "Errors contains error details when the request fails", "type": "array", "items": {"type": "object"}}, "meta": {"description": "Meta contains metadata about the response (pagination info, messages, etc.)", "type": "object"}}}, "handler.CreateNotificationRequest": {"type": "object", "required": ["click_action", "description", "user_id"], "properties": {"click_action": {"allOf": [{"$ref": "#/definitions/model.ClickAction"}], "example": "OPEN_URL"}, "click_action_url": {"type": "string", "example": "https://example.com/message/123"}, "description": {"type": "string", "example": "You have a new message"}, "is_reminder": {"type": "boolean", "example": false}, "notif_category_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "notif_type_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "user_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}}}, "handler.RegisterTokenRequest": {"type": "object", "required": ["sso_id", "token", "user_source", "user_type"], "properties": {"sso_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "token": {"type": "string", "example": "fcm_token_string"}, "user_source": {"allOf": [{"$ref": "#/definitions/model.UserSource"}], "example": "mobile_app"}, "user_type": {"allOf": [{"$ref": "#/definitions/model.UserType"}], "example": "customer"}}}, "handler.SendPushNotificationRequest": {"type": "object"}, "model.ClickAction": {"type": "string", "enum": ["OPEN_URL", "OPEN_APP"], "x-enum-varnames": ["ClickActionOpenURL", "ClickActionOpenApp"]}, "model.FCMToken": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "sso_id": {"type": "string"}, "token": {"type": "string"}, "updated_at": {"type": "string"}, "user_source": {"$ref": "#/definitions/model.UserSource"}, "user_type": {"$ref": "#/definitions/model.UserType"}}}, "model.Notification": {"type": "object", "properties": {"click_action": {"$ref": "#/definitions/model.ClickAction"}, "click_action_url": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "is_reminder": {"type": "boolean"}, "notif_category_id": {"type": "string"}, "notif_type_id": {"type": "string"}, "read_at": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}}}, "model.UserSource": {"type": "string", "enum": ["crm", "chat", "notification_service"], "x-enum-varnames": ["UserSourceCRM", "UserSourceChat", "UserSourceNotificationSvc"]}, "model.UserType": {"type": "string", "enum": ["admin", "supervisor", "agent"], "x-enum-varnames": ["UserTypeAdmin", "UserTypeSupervisor", "UserTypeAgent"]}}}