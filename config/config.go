package config

import (
	"fmt"
	"net/url"
	"os"
	"strings"
	"time"

	stdLog "log"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

func GetAppName() string {
	return viper.GetString("APP_NAME")
}

func GetAppEnv() string {
	return viper.GetString("APP_ENV")
}

func GetEnv() string {
	return viper.GetString("APP_ENV")
}

func GetServerAddress() string {
	port := viper.GetString("APP_PORT")
	if port == "" {
		return ":4000"
	}
	return ":" + strings.TrimPrefix(port, ":")
}

func GetDatabaseDsn(prefix string) string {
	username := viper.GetString("DATABASE_USERNAME")
	pass := url.QueryEscape(viper.GetString("DATABASE_PASSWORD"))
	host := viper.GetString("DATABASE_HOST")
	port := viper.GetString("DATABASE_PORT")
	dbName := viper.GetString("DATABASE_NAME")

	return fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", username, pass, host, port, dbName)
}

func GetDatabaseConnMaxLifetime(prefix string) time.Duration {
	lifetimeStr := viper.GetString("DATABASE_CONN_MAX_LIFETIME")
	lifetime, err := time.ParseDuration(lifetimeStr)
	if err != nil {
		return 15 * time.Minute
	}
	return lifetime
}

func GetDatabaseConnMaxIdleTime(prefix string) time.Duration {
	idleTimeStr := viper.GetString("DATABASE_CONN_MAX_IDLE_TIME")
	idleTime, err := time.ParseDuration(idleTimeStr)
	if err != nil {
		return 5 * time.Minute
	}
	return idleTime
}

func GetDatabaseMaxOpenConn(prefix string) int {
	return viper.GetInt("DATABASE_MAX_OPEN_CONN")
}

func GetDatabaseMaxIdleConn(prefix string) int {
	return viper.GetInt("DATABASE_MAX_IDLE_CONN")
}

func GetDatabaseName(prefix string) string {
	return viper.GetString("DATABASE_NAME")
}

func GetDatabasePool() int {
	return viper.GetInt("DATABASE_POOL")
}

func GetDatabaseIdleTimeout() time.Duration {
	timeoutStr := viper.GetString("DATABASE_IDLE_TIMEOUT")
	timeout, err := time.ParseDuration(timeoutStr)
	if err != nil {
		timeout = time.Minute
	}

	return timeout
}

func GetKafkaBrokers() []string {
	brokersStr := viper.GetString("KAFKA_BROKERS")
	if brokersStr == "" {
		return []string{}
	}

	// Split by comma
	brokers := strings.Split(brokersStr, ",")

	// Process each broker to remove kafka:// prefix if present
	for i, broker := range brokers {
		broker = strings.TrimSpace(broker)
		if strings.HasPrefix(broker, "kafka://") {
			brokers[i] = strings.TrimPrefix(broker, "kafka://")
		} else {
			brokers[i] = broker
		}
	}

	return brokers
}

func GetKafkaSasl() bool {
	return viper.GetBool("KAFKA_SASL")
}

func GetKafkaUsername() string {
	return viper.GetString("KAFKA_USERNAME")
}

func GetKafkaSaslUsername() string {
	return viper.GetString("KAFKA_USERNAME")
}

func GetKafkaPassword() string {
	return viper.GetString("KAFKA_PASSWORD")
}

func GetKafkaSaslPassword() string {
	return viper.GetString("KAFKA_PASSWORD")
}

func GetKafkaPrefix() string {
	return viper.GetString("KAFKA_PREFIX")
}

func GetKafkaCert() string {
	certFile := viper.GetString("KAFKA_CERT_FILE")
	if certFile == "" {
		return ""
	}

	certData, err := os.ReadFile(certFile)
	if err != nil {
		stdLog.Printf("Error reading Kafka certificate file: %v", err)
		return ""
	}

	return string(certData)
}

func GetDDAgentHost() string {
	return viper.GetString("DD_AGENT_HOST")
}

func GetDDAgentPort() string {
	port := viper.GetString("DD_AGENT_PORT")
	if port != "" {
		return port
	}

	return "8126"
}

func GetDDStatsdHost() string {
	return viper.GetString("STATSD_HOST")
}

func GetDDStatsdPort() string {
	port := viper.GetString("STATSD_PORT")
	if port != "" {
		return port
	}

	return "8125"
}

func TracerEnabled() bool {
	return viper.GetBool("DATADOG_TRACER")
}

func Init() {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		stdLog.Printf("No .env file found, using system environment variables")
	}

	// Configure Viper to read from environment variables
	viper.AutomaticEnv()

	// Set default values
	setDefaults()
}

func setDefaults() {
	// Application defaults
	viper.SetDefault("APP_NAME", "notification-service")
	viper.SetDefault("APP_ENV", "development")
	viper.SetDefault("APP_PORT", "4000")

	// Database defaults
	viper.SetDefault("DATABASE_POOL", 10)
	viper.SetDefault("DATABASE_IDLE_TIMEOUT", "1m")
	viper.SetDefault("DATABASE_CONN_MAX_LIFETIME", "15m")
	viper.SetDefault("DATABASE_CONN_MAX_IDLE_TIME", "5m")
	viper.SetDefault("DATABASE_MAX_OPEN_CONN", 10)
	viper.SetDefault("DATABASE_MAX_IDLE_CONN", 5)
}
